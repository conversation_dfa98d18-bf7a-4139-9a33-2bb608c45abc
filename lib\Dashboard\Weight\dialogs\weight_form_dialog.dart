import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';

class WeightFormDialog extends StatefulWidget {
  final List<CattleIsar> cattle;
  final VoidCallback? onRecordAdded;
  final WeightRecordIsar? existingRecord; // For editing
  final CattleIsar? preSelectedCattle; // For pre-selecting specific cattle

  const WeightFormDialog({
    Key? key,
    required this.cattle,
    this.onRecordAdded,
    this.existingRecord,
    this.preSelectedCattle,
  }) : super(key: key);

  @override
  State<WeightFormDialog> createState() => _WeightFormDialogState();
}

class _WeightFormDialogState extends State<WeightFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final WeightService _weightService = WeightService();

  // Form controllers
  final _weightController = TextEditingController();
  final _notesController = TextEditingController();
  final _measuredByController = TextEditingController();
  final _bodyConditionNotesController = TextEditingController();

  // Form data
  CattleIsar? _selectedCattle;
  DateTime _measurementDate = DateTime.now();
  String _weightUnit = 'kg';
  String _measurementMethod = 'scale';
  String _measurementLocation = 'barn';
  String _measurementQuality = 'good';
  String _healthStatus = 'healthy';
  String _feedingStatus = 'normal';
  double? _bodyConditionScore;
  bool _isPregnant = false;
  int? _pregnancyStage;
  String _season = 'spring';
  String _feedQuality = 'good';
  bool _isEstimate = false;
  double _confidenceLevel = 1.0;

  // Loading state
  bool _isSaving = false;

  // Colors - Green theme for header and save button
  static const _headerColor = Color(0xFF2E7D32);         // Green for header and save button
  static const _measurementColor = Color(0xFF7B1FA2);    // Purple for Measurement Details
  static const _notesColor = Color(0xFF1976D2);          // Blue for Notes

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingRecord != null) {
      final record = widget.existingRecord!;
      _weightController.text = record.weight.toString();
      _notesController.text = record.notes ?? '';
      _measuredByController.text = record.measuredBy ?? '';
      _bodyConditionNotesController.text = record.bodyConditionNotes ?? '';
      
      _selectedCattle = widget.cattle.firstWhere(
        (cattle) => cattle.businessId == record.cattleBusinessId,
        orElse: () => widget.cattle.first,
      );
      
      _measurementDate = record.measurementDate ?? DateTime.now();
      _weightUnit = record.weightUnit ?? 'kg';
      _measurementMethod = record.measurementMethod ?? 'scale';
      _measurementLocation = record.measurementLocation ?? 'barn';
      _measurementQuality = record.measurementQuality ?? 'good';
      _healthStatus = record.healthStatus ?? 'healthy';
      _feedingStatus = record.feedingStatus ?? 'normal';
      _bodyConditionScore = record.bodyConditionScore;
      _isPregnant = record.isPregnant ?? false;
      _pregnancyStage = record.pregnancyStage;
      _season = record.season ?? 'spring';
      _feedQuality = record.feedQuality ?? 'good';
      _isEstimate = record.isEstimate ?? false;
      _confidenceLevel = record.confidenceLevel ?? 1.0;
    } else {
      // Use pre-selected cattle if provided, otherwise use first cattle
      _selectedCattle = widget.preSelectedCattle ??
          (widget.cattle.isNotEmpty ? widget.cattle.first : null);
    }
  }

  @override
  void dispose() {
    _weightController.dispose();
    _notesController.dispose();
    _measuredByController.dispose();
    _bodyConditionNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: screenSize.width * 0.9,
          maxHeight: screenSize.height * 0.9,
          minWidth: 300,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _headerColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: const Icon(
                      Icons.scale,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.existingRecord != null ? 'Edit Weight Record' : 'Add Weight Record',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildCompactForm(),
                    ],
                  ),
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: _isSaving ? null : () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isSaving ? null : _saveRecord,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _headerColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isSaving
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : Text(
                              widget.existingRecord != null ? 'Update' : 'Save',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Input styling constants like transaction dialog
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding = EdgeInsets.symmetric(horizontal: 12, vertical: 8);

  Widget _buildCompactForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Cattle selection
        DropdownButtonFormField<CattleIsar>(
          value: _selectedCattle,
          decoration: InputDecoration(
            labelText: 'Select Cattle',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: Icon(Icons.pets, color: _headerColor),
            contentPadding: _inputContentPadding,
            constraints: _inputDecorationConstraints,
          ),
          isExpanded: true,
          items: widget.cattle.map((cattle) {
            final cattleName = cattle.name ?? 'Unknown';
            final tagId = cattle.tagId ?? '';
            final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
            return DropdownMenuItem(
              value: cattle,
              child: Text(displayName, overflow: TextOverflow.ellipsis),
            );
          }).toList(),
          onChanged: (value) => setState(() => _selectedCattle = value),
          validator: (value) => value == null ? 'Please select cattle' : null,
        ),
        const SizedBox(height: 16),

        // Weight input
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _weightController,
                decoration: InputDecoration(
                  labelText: 'Weight',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: Icon(Icons.scale, color: _measurementColor),
                  contentPadding: _inputContentPadding,
                  constraints: _inputDecorationConstraints,
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Please enter weight';
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0) return 'Please enter valid weight';
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _weightUnit,
                decoration: InputDecoration(
                  labelText: 'Unit',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  contentPadding: _inputContentPadding,
                  constraints: _inputDecorationConstraints,
                ),
                items: const [
                  DropdownMenuItem(value: 'kg', child: Text('kg')),
                  DropdownMenuItem(value: 'lbs', child: Text('lbs')),
                ],
                onChanged: (value) => setState(() => _weightUnit = value!),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Measurement date
        InkWell(
          onTap: _selectDate,
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Measurement Date',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: Icon(Icons.calendar_today, color: _headerColor),
              contentPadding: _inputContentPadding,
              constraints: _inputDecorationConstraints,
            ),
            child: Text(
              DateFormat('MMMM dd, yyyy').format(_measurementDate),
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Measurement method
        DropdownButtonFormField<String>(
          value: _measurementMethod,
          decoration: InputDecoration(
            labelText: 'Measurement Method',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: Icon(Icons.straighten, color: _measurementColor),
            contentPadding: _inputContentPadding,
            constraints: _inputDecorationConstraints,
          ),
          isExpanded: true,
          items: const [
            DropdownMenuItem(value: 'scale', child: Text('Scale')),
            DropdownMenuItem(value: 'tape', child: Text('Tape Measure')),
            DropdownMenuItem(value: 'visual_estimate', child: Text('Visual Estimate')),
          ],
          onChanged: (value) => setState(() => _measurementMethod = value!),
        ),
        const SizedBox(height: 16),

        // Notes
        TextFormField(
          controller: _notesController,
          decoration: InputDecoration(
            labelText: 'Notes (Optional)',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: Icon(Icons.note, color: _notesColor),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          maxLines: 3,
        ),
      ],
    );
  }





  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _measurementDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() => _measurementDate = date);
    }
  }

  Future<void> _saveRecord() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCattle == null) return;

    setState(() => _isSaving = true);

    try {
      final record = WeightRecordIsar()
        ..cattleBusinessId = _selectedCattle!.businessId
        ..weight = double.parse(_weightController.text)
        ..weightUnit = _weightUnit
        ..measurementDate = _measurementDate
        ..measurementMethod = _measurementMethod
        ..measurementLocation = _measurementLocation
        ..measuredBy = _measuredByController.text.isNotEmpty ? _measuredByController.text : null
        ..bodyConditionScore = _bodyConditionScore
        ..bodyConditionNotes = _bodyConditionNotesController.text.isNotEmpty ? _bodyConditionNotesController.text : null
        ..healthStatus = _healthStatus
        ..feedingStatus = _feedingStatus
        ..isPregnant = _isPregnant
        ..pregnancyStage = _pregnancyStage
        ..season = _season
        ..feedQuality = _feedQuality
        ..measurementQuality = _measurementQuality
        ..notes = _notesController.text.isNotEmpty ? _notesController.text : null
        ..isEstimate = _isEstimate
        ..confidenceLevel = _confidenceLevel;

      if (widget.existingRecord != null) {
        record.id = widget.existingRecord!.id;
        record.businessId = widget.existingRecord!.businessId;
        record.createdAt = widget.existingRecord!.createdAt;
        await _weightService.updateWeightRecord(record);
      } else {
        await _weightService.addWeightRecord(record);
      }

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.existingRecord != null 
              ? 'Weight record updated successfully'
              : 'Weight record added successfully',
        );
        
        widget.onRecordAdded?.call();
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error saving weight record: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
