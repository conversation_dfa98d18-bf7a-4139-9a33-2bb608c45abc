import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../../widgets/universal_record_card.dart';

/// A widget that displays a single transaction item in a list using Universal Record Card
class TransactionListItem extends StatelessWidget {
  final TransactionIsar transaction;
  final CategoryIsar category;
  final VoidCallback onDelete;
  final VoidCallback onEdit;
  final bool compact;

  const TransactionListItem({
    Key? key,
    required this.transaction,
    required this.category,
    required this.onDelete,
    required this.onEdit,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine if income or expense for dynamic colors
    final isIncome = transaction.categoryType.toLowerCase() == 'income';
    final amountColor = isIncome ? Colors.green : Colors.red;
    final formattedAmount = '${isIncome ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}';
    final formattedDate = DateFormat('MMM dd, yyyy').format(transaction.date);

    // Use the simplified universal record card
    return UniversalRecordCard(
      row1Left: formattedDate,
      row1Right: formattedAmount,
      row2Left: transaction.category,
      row2Right: transaction.paymentMethod,
      row2LeftIcon: transaction.icon, // Category icon
      row2RightIcon: _getPaymentMethodIcon(transaction.paymentMethod), // Payment method icon
      notes: transaction.description.isNotEmpty ? transaction.description : null,
      primaryColor: UniversalRecordTheme.transactionPrimary,
      row1RightColor: amountColor,
      onEdit: onEdit,
      onDelete: onDelete,
      onTap: onEdit,
      compact: compact,
    );
  }

  IconData _getPaymentMethodIcon(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'credit card':
        return Icons.credit_card;
      case 'debit card':
        return Icons.payment;
      case 'bank transfer':
        return Icons.account_balance;
      case 'mobile payment':
        return Icons.phone_android;
      case 'check':
        return Icons.receipt;
      default:
        return Icons.payment;
    }
  }
}