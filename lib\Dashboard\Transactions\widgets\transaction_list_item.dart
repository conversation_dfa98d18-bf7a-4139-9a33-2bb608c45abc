import 'package:flutter/material.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../../widgets/universal_record_card.dart';

/// A widget that displays a single transaction item in a list using Universal Record Card
class TransactionListItem extends StatelessWidget {
  final TransactionIsar transaction;
  final CategoryIsar category;
  final VoidCallback onDelete;
  final VoidCallback onEdit;
  final bool compact;

  const TransactionListItem({
    Key? key,
    required this.transaction,
    required this.category,
    required this.onDelete,
    required this.onEdit,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Create configuration using the universal factory
    final config = UniversalRecordConfigFactory.transaction(
      date: transaction.date,
      title: transaction.title,
      amount: transaction.amount,
      categoryType: transaction.categoryType,
      category: transaction.category,
      paymentMethod: transaction.paymentMethod,
      description: transaction.description.isNotEmpty ? transaction.description : null,
      categoryIcon: transaction.icon,
      onEdit: onEdit,
      onDelete: onDelete,
    );

    // Use the universal record card
    return UniversalRecordCard(
      config: config,
      onTap: onEdit,
      compact: compact,
    );
  }
}