import 'package:flutter/material.dart';
import '../models/transaction_isar.dart';
import '../../widgets/universal_record_card.dart';

/// Transaction Record Card using Universal Record Card System
/// Demonstrates the universal system implementation in the transaction module
class TransactionRecordCard extends StatelessWidget {
  final TransactionIsar transaction;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ValueChanged<bool?>? onSelectionChanged;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool compact;

  const TransactionRecordCard({
    Key? key,
    required this.transaction,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.onEdit,
    this.onDelete,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Create configuration using the universal factory
    final config = UniversalRecordConfigFactory.transaction(
      date: transaction.date,
      title: transaction.title,
      amount: transaction.amount,
      categoryType: transaction.categoryType,
      category: transaction.category,
      paymentMethod: transaction.paymentMethod,
      description: transaction.description.isNotEmpty ? transaction.description : null,
      categoryIcon: transaction.icon,
      onEdit: onEdit ?? () {},
      onDelete: onDelete ?? () {},
    );

    // Use the universal record card
    return UniversalRecordCard(
      config: config,
      isSelected: isSelected,
      isSelectionMode: isSelectionMode,
      onTap: onTap,
      onLongPress: onLongPress,
      onSelectionChanged: onSelectionChanged,
      compact: compact,
    );
  }
}
