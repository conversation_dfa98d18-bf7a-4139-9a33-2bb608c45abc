// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';

import '../services/transactions_handler.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../widgets/universal_record_card.dart';
import '../../../utils/message_utils.dart';

class TransactionsListTab extends StatefulWidget {
  final VoidCallback onRefresh;

  const TransactionsListTab({Key? key, required this.onRefresh})
      : super(key: key);

  @override
  State<TransactionsListTab> createState() => _TransactionsListTabState();
}

class _TransactionsListTabState extends State<TransactionsListTab> {
  final TransactionsHandler _transactionsHandler =
      GetIt.instance<TransactionsHandler>();
  List<TransactionIsar> _transactions = [];
  List<CategoryIsar> _categories = [];
  bool _isLoading = true;
  final _searchQuery = TextEditingController();

  // For date filtering

  // Currency settings

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final transactions = await _transactionsHandler.getAllTransactions();
      final categories = await _transactionsHandler.getAllCategories();

      if (!mounted) return;

      setState(() {
        _transactions = transactions;
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading transactions: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        FinancialMessageUtils.showError(context, 'Error loading transactions');
      }
    }
  }

  @override
  void dispose() {
    _searchQuery.dispose();
    super.dispose();
  }

  Future<void> _showAddTransactionDialog() async {
    await showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: _categories,
        onSave: (transaction) async {
          try {
            await _transactionsHandler.addTransaction(transaction);
            await _loadData();
          } catch (e) {
            debugPrint('Error adding transaction: $e');
            if (mounted) {
              FinancialMessageUtils.showError(context, 'Error adding transaction');
            }
          }
        },
      ),
    );
  }

  Future<void> _editTransaction(TransactionIsar transaction) async {
    await showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        transaction: transaction,
        categories: _categories,
        onSave: (updatedTransaction) async {
          try {
            await _transactionsHandler.updateTransaction(updatedTransaction);
            await _loadData();
            if (mounted) {
              FinancialMessageUtils.showSuccess(context, 'Transaction updated successfully');
            }
          } catch (e) {
            debugPrint('Error updating transaction: $e');
            if (mounted) {
              FinancialMessageUtils.showError(context, 'Error updating transaction');
            }
          }
        },
      ),
    );
  }

  Future<void> _deleteTransaction(TransactionIsar transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text('Are you sure you want to delete "${transaction.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _transactionsHandler.deleteTransaction(transaction.transactionId);
        await _loadData();
        if (mounted) {
          FinancialMessageUtils.showSuccess(context, 'Transaction deleted successfully');
        }
      } catch (e) {
        debugPrint('Error deleting transaction: $e');
        if (mounted) {
          FinancialMessageUtils.showError(context, 'Error deleting transaction');
        }
      }
    }
  }

  void _showTransactionOptions(TransactionIsar transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Transaction'),
              onTap: () {
                Navigator.pop(context);
                _editTransaction(transaction);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Transaction', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _deleteTransaction(transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('No transactions found'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _showAddTransactionDialog,
              child: const Text('Add Transaction'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];

        // Determine if income or expense for dynamic colors
        final isIncome = transaction.categoryType.toLowerCase() == 'income';
        final amountColor = isIncome ? Colors.green : Colors.red;
        final formattedAmount = '${isIncome ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}';
        final formattedDate = DateFormat('MMM dd, yyyy').format(transaction.date);

        // Use the simplified universal record card
        return UniversalRecordCard(
          row1Left: formattedDate,
          row1Right: formattedAmount,
          row2Left: transaction.category,
          row2Right: transaction.paymentMethod,
          notes: transaction.description.isNotEmpty ? transaction.description : null,
          primaryColor: UniversalRecordTheme.transactionPrimary,
          row1RightColor: amountColor,
          onEdit: () => _editTransaction(transaction),
          onDelete: () => _deleteTransaction(transaction),
          onTap: () => _editTransaction(transaction),
          onLongPress: () => _showTransactionOptions(transaction),
        );
      },
    );
  }
}
