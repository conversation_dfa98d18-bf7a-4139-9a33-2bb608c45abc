import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';

/// Universal Record Card System
/// Based on the Weight Card as the ideal template
/// Supports all record types across modules with consistent layout and behavior

class UniversalRecordCard extends StatelessWidget {
  // Simple variables for easy configuration
  final DateTime? date;
  final String primaryValue;
  final String row2Left;
  final String row2Right;
  final String? row3Left;
  final String? row3Right;
  final String? notes;
  final Color primaryColor;
  final Color? amountColor; // For dynamic red/green based on income/expense
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool isSelectionMode;
  final ValueChanged<bool?>? onSelectionChanged;
  final bool compact;

  const UniversalRecordCard({
    Key? key,
    required this.date,
    required this.primaryValue,
    required this.row2Left,
    required this.row2Right,
    this.row3Left,
    this.row3Right,
    this.notes,
    required this.primaryColor,
    this.amountColor,
    this.onEdit,
    this.onDelete,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onSelectionChanged,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final formattedDate = date != null
        ? DateFormat('MMM dd, yyyy').format(date!)
        : 'No date';

    return Card(
      margin: compact
          ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(
          color: Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: isSelectionMode
            ? () => onSelectionChanged?.call(!isSelected)
            : onTap,
        onLongPress: onLongPress,
        child: Container(
          padding: compact
              ? const EdgeInsets.all(12)
              : const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Date + Primary Value + Kebab Menu
              _buildRow1(),
              const SizedBox(height: 8),
              // Row 2: Left + Right content
              _buildRow2(),
              // Row 3: Optional third row
              if (row3Left != null || row3Right != null) ...[
                const SizedBox(height: 8),
                _buildRow3(),
              ],
              // Notes row (if available)
              if (notes?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                _buildNotesRow(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRow1() {
    final formattedDate = date != null
        ? DateFormat('MMM dd, yyyy').format(date!)
        : 'No date';
    return IntrinsicHeight(
      child: Row(
        children: [
          if (isSelectionMode)
            SizedBox(
              width: 56,
              child: Checkbox(
                value: isSelected,
                onChanged: onSelectionChanged,
                activeColor: AppColors.primary,
              ),
            ),
          // Status indicator - 4px vertical stripe
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          // Date - flexible width
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  size: 20,
                  color: const Color(0xFF303F9F), // Indigo
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    formattedDate,
                    style: const TextStyle(
                      color: Color(0xFF303F9F), // Indigo
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Primary value (amount) - right aligned
          Container(
            constraints: const BoxConstraints(minWidth: 80),
            child: Text(
              primaryValue,
              style: TextStyle(
                color: amountColor ?? primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
            ),
          ),
          // Kebab menu for edit/delete
          if (!isSelectionMode && (onEdit != null || onDelete != null))
            SizedBox(
              width: 24,
              child: _buildKebabMenu(),
            ),
        ],
      ),
    );
  }

  Widget _buildRow2() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        // Left content - flexible width
        Expanded(
          child: Text(
            row2Left,
            style: const TextStyle(
              color: Color(0xFF7B1FA2), // Purple
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 16),
        // Right content - right aligned
        Container(
          constraints: const BoxConstraints(minWidth: 100),
          child: Text(
            row2Right,
            style: const TextStyle(
              color: Color(0xFF2E7D32), // Green
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.right,
            maxLines: 1,
          ),
        ),
        if (!isSelectionMode && (onEdit != null || onDelete != null))
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildRow3() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        // Left content - flexible width
        Expanded(
          child: Text(
            row3Left ?? '',
            style: const TextStyle(
              color: Color(0xFF795548), // Brown
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 16),
        // Right content - right aligned
        if (row3Right?.isNotEmpty == true)
          Container(
            constraints: const BoxConstraints(minWidth: 100),
            child: Text(
              row3Right!,
              style: const TextStyle(
                color: Color(0xFF795548), // Brown
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
            ),
          ),
        if (!isSelectionMode && (onEdit != null || onDelete != null))
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildNotesRow() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            notes!,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF16A085), // Teal
              fontStyle: FontStyle.italic,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (!isSelectionMode && (onEdit != null || onDelete != null))
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildKebabMenu() {
    return PopupMenuButton<String>(
      icon: const Icon(
        Icons.more_vert,
        color: Color(0xFF303F9F), // Indigo
      ),
      onSelected: (String choice) {
        if (choice == 'Edit' && onEdit != null) {
          onEdit!();
        } else if (choice == 'Delete' && onDelete != null) {
          onDelete!();
        }
      },
      itemBuilder: (BuildContext context) => [
        if (onEdit != null)
          const PopupMenuItem(
            value: 'Edit',
            child: Row(
              children: [
                Icon(Icons.edit_outlined),
                SizedBox(width: 8),
                Text('Edit'),
              ],
            ),
          ),
        if (onDelete != null)
          const PopupMenuItem(
            value: 'Delete',
            child: Row(
              children: [
                Icon(
                  Icons.delete_outline,
                  color: Colors.red,
                ),
                SizedBox(width: 8),
                Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
          ),
      ],
    );
  }

}

/// Simple theme constants for consistent colors across all record types
class UniversalRecordTheme {
  // Module-specific primary colors for the 4px stripe
  static const Color weightPrimary = Color(0xFF1976D2);       // Blue
  static const Color healthPrimary = Color(0xFF00796B);       // Teal
  static const Color milkPrimary = Color(0xFF2E7D32);         // Green
  static const Color breedingPrimary = Color(0xFFD32F2F);     // Red
  static const Color eventPrimary = Color(0xFF7B1FA2);        // Purple
  static const Color pregnancyPrimary = Color(0xFF9C27B0);    // Purple (distinct from events)
  static const Color deliveryPrimary = Color(0xFF2E7D32);     // Green (same as milk for consistency)
  static const Color transactionPrimary = Color(0xFFFF8F00);  // Orange
}

  /// Health Record Configuration
  static UniversalRecordConfig health({
    required DateTime? date,
    required String condition,
    required String treatment,
    required String cattleName,
    required String? tagId,
    String? medicine,
    double? dose,
    String? dosageUnit,
    String? veterinarian,
    double? cost,
    bool? isResolved,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.healthPrimary,
      dateField: UniversalRecordField.date(date),
      primaryValueField: UniversalRecordField.primaryValue(
        condition,
        color: UniversalRecordTheme.healthPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField(
        value: treatment,
        color: UniversalRecordTheme.methodColor,
        icon: Icons.medical_services,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        if (medicine?.isNotEmpty == true)
          UniversalRecordField.metric(
            'Medicine',
            '$medicine${dose != null ? ' (${dose.toString()} ${dosageUnit ?? ''})' : ''}',
            Icons.medication,
          ),
        if (veterinarian?.isNotEmpty == true)
          UniversalRecordField.metric('Vet', veterinarian!, Icons.person),
        if (cost != null)
          UniversalRecordField.cost(cost),
        if (isResolved == true)
          UniversalRecordField.status('Resolved', Colors.green),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Milk Record Configuration
  static UniversalRecordConfig milk({
    required DateTime? date,
    required double totalAmount,
    required String cattleName,
    required String? tagId,
    double? morningAmount,
    double? eveningAmount,
    double? fatContent,
    double? pricePerLiter,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.milkPrimary,
      dateField: UniversalRecordField.date(date),
      primaryValueField: UniversalRecordField.primaryValue(
        '${totalAmount.toStringAsFixed(1)} L',
        color: UniversalRecordTheme.milkPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField(
        value: 'Total Yield',
        color: UniversalRecordTheme.methodColor,
        icon: Icons.water_drop,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        if (morningAmount != null)
          UniversalRecordField.metric(
            'Morning',
            '${morningAmount.toStringAsFixed(1)} L',
            Icons.wb_sunny_outlined,
          ),
        if (eveningAmount != null)
          UniversalRecordField.metric(
            'Evening',
            '${eveningAmount.toStringAsFixed(1)} L',
            Icons.nightlight_outlined,
          ),
        if (fatContent != null)
          UniversalRecordField.metric(
            'Fat',
            '${fatContent.toStringAsFixed(1)}%',
            Icons.opacity,
          ),
        if (pricePerLiter != null)
          UniversalRecordField.cost(pricePerLiter),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Breeding Record Configuration
  static UniversalRecordConfig breeding({
    required DateTime? breedingDate,
    required String status,
    required String cattleName,
    required String? tagId,
    String? method,
    String? bullOrSemen,
    DateTime? expectedDate,
    double? cost,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
    VoidCallback? onStatusTap,
  }) {
    final statusColor = _getBreedingStatusColor(status);

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.breedingPrimary,
      dateField: UniversalRecordField.date(breedingDate),
      primaryValueField: UniversalRecordField.status(status, statusColor),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: method != null
          ? UniversalRecordField.method(method, Icons.favorite)
          : null,
      row3Fields: [
        if (bullOrSemen?.isNotEmpty == true)
          UniversalRecordField.metric('Bull/Semen', bullOrSemen!, Icons.pets),
        if (expectedDate != null)
          UniversalRecordField.metric(
            'Expected',
            DateFormat('MMM dd').format(expectedDate),
            Icons.event,
          ),
        if (cost != null)
          UniversalRecordField.cost(cost),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
        if (onStatusTap != null)
          UniversalRecordAction(
            label: 'Change Status',
            icon: Icons.update,
            onPressed: onStatusTap,
          ),
      ],
    );
  }

  /// Event Record Configuration
  static UniversalRecordConfig event({
    required String title,
    required DateTime? eventDate,
    required String cattleName,
    required String? tagId,
    String? eventType,
    String? priority,
    bool isCompleted = false,
    bool isMissed = false,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final statusColor = isCompleted
        ? Colors.green
        : isMissed
            ? Colors.red
            : UniversalRecordTheme.eventPrimary;

    final statusText = isCompleted
        ? 'Completed'
        : isMissed
            ? 'Missed'
            : 'Pending';

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.eventPrimary,
      dateField: UniversalRecordField.date(eventDate),
      primaryValueField: UniversalRecordField.primaryValue(
        title,
        color: UniversalRecordTheme.eventPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField.status(statusText, statusColor),
      row3Fields: [
        if (eventType?.isNotEmpty == true)
          UniversalRecordField.metric('Type', eventType!, Icons.category),
        if (priority?.isNotEmpty == true)
          UniversalRecordField.metric('Priority', priority!, Icons.priority_high),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Pregnancy Record Configuration
  static UniversalRecordConfig pregnancy({
    required DateTime? startDate,
    required String status,
    required String cattleName,
    required String? tagId,
    DateTime? expectedCalvingDate,
    DateTime? actualCalvingDate,
    String? breedingRecordId,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
    VoidCallback? onStatusTap,
  }) {
    final statusColor = _getPregnancyStatusColor(status);
    final pregnancyStage = _calculatePregnancyStage(startDate);
    final daysRemaining = _calculateDaysRemaining(startDate, expectedCalvingDate);

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.pregnancyPrimary,
      dateField: UniversalRecordField.date(startDate),
      primaryValueField: UniversalRecordField.status(status, statusColor),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField(
        value: pregnancyStage,
        color: UniversalRecordTheme.pregnancyPrimary,
        icon: Icons.pregnant_woman,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        if (expectedCalvingDate != null)
          UniversalRecordField.metric(
            status.toLowerCase() == 'completed' ? 'Delivery Date' : 'Expected Date',
            DateFormat('MMM dd, yyyy').format(
              status.toLowerCase() == 'completed' && actualCalvingDate != null
                  ? actualCalvingDate
                  : expectedCalvingDate
            ),
            Icons.today,
          ),
        UniversalRecordField.metric(
          status.toLowerCase() == 'completed' ? 'Duration' : 'Remaining',
          daysRemaining,
          Icons.hourglass_bottom,
        ),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
        if (onStatusTap != null)
          UniversalRecordAction(
            label: 'Change Status',
            icon: Icons.update,
            onPressed: onStatusTap,
          ),
      ],
    );
  }

  /// Delivery Record Configuration
  static UniversalRecordConfig delivery({
    required DateTime? deliveryDate,
    required String deliveryType,
    required String cattleName,
    required String? tagId,
    int? calfCount,
    List<Map<String, dynamic>>? calfDetails,
    bool? hadComplications,
    String? complicationDetails,
    String? veterinarian,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final calfCountText = calfCount != null ? calfCount.toString() : '1';
    final complicationStatus = hadComplications == true ? 'Had Complications' : 'Normal';
    final complicationColor = hadComplications == true ? Colors.red : Colors.green;

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.deliveryPrimary,
      dateField: UniversalRecordField.date(deliveryDate),
      primaryValueField: UniversalRecordField.primaryValue(
        deliveryType,
        color: UniversalRecordTheme.deliveryPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField.metric(
        'Calves',
        calfCountText,
        Icons.child_care,
      ),
      row3Fields: [
        UniversalRecordField.status(complicationStatus, complicationColor),
        if (veterinarian?.isNotEmpty == true)
          UniversalRecordField.metric('Vet', veterinarian!, Icons.person),
        if (calfDetails?.isNotEmpty == true)
          UniversalRecordField.metric(
            'Calf Details',
            '${calfDetails!.length} calf${calfDetails.length > 1 ? 'ves' : ''} recorded',
            Icons.baby_changing_station,
          ),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Transaction Record Configuration
  static UniversalRecordConfig transaction({
    required DateTime? date,
    required String title,
    required double amount,
    required String categoryType, // 'Income' or 'Expense'
    required String category,
    required String paymentMethod,
    String? description,
    IconData? categoryIcon,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final isIncome = categoryType.toLowerCase() == 'income';
    final amountColor = isIncome ? Colors.green : Colors.red;
    final formattedAmount = '${isIncome ? '+' : '-'}\$${amount.toStringAsFixed(2)}';
    final statusIcon = isIncome ? Icons.arrow_upward : Icons.arrow_downward;

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.transactionPrimary,
      dateField: UniversalRecordField.date(date),
      primaryValueField: UniversalRecordField.primaryValue(
        formattedAmount,
        color: amountColor,
      ),
      cattleField: UniversalRecordField(
        value: title,
        color: UniversalRecordTheme.transactionPrimary,
        icon: categoryIcon ?? (isIncome ? Icons.trending_up : Icons.trending_down),
        fontWeight: FontWeight.bold,
      ),
      secondaryField: UniversalRecordField(
        value: categoryType,
        color: amountColor,
        icon: statusIcon,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        UniversalRecordField.metric('Category', category, Icons.category),
        UniversalRecordField.metric('Payment', paymentMethod, Icons.payment),
      ],
      notesField: description?.isNotEmpty == true ? UniversalRecordField.notes(description!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  // Helper methods for weight module
  static IconData _getWeightMethodIcon(String? method) {
    switch (method?.toLowerCase()) {
      case 'scale':
        return Icons.scale;
      case 'tape':
        return Icons.straighten;
      case 'visual_estimate':
        return Icons.visibility;
      default:
        return Icons.straighten;
    }
  }

  static String _getWeightMethodDisplayName(String? method) {
    switch (method?.toLowerCase()) {
      case 'scale':
        return 'Scale';
      case 'tape':
        return 'Tape';
      case 'visual_estimate':
        return 'Visual';
      default:
        return method ?? 'Unknown';
    }
  }

  // Helper method for breeding status colors
  static Color _getBreedingStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'successful':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return UniversalRecordTheme.breedingPrimary;
    }
  }

  // Helper method for pregnancy status colors
  static Color _getPregnancyStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return const Color(0xFF1976D2); // Blue
      case 'confirmed':
        return const Color(0xFF2E7D32); // Green
      case 'abortion':
        return const Color(0xFFD32F2F); // Red
      case 'completed':
        return const Color(0xFF9C27B0); // Purple
      default:
        return Colors.grey;
    }
  }

  // Helper method to calculate pregnancy stage
  static String _calculatePregnancyStage(DateTime? startDate) {
    if (startDate == null) return 'Unknown Stage';

    final now = DateTime.now();
    final daysSinceStart = now.difference(startDate).inDays;

    if (daysSinceStart < 90) {
      return 'Early Stage';
    } else if (daysSinceStart < 180) {
      return 'Mid Stage';
    } else {
      return 'Late Stage';
    }
  }

  // Helper method to calculate days remaining until due date
  static String _calculateDaysRemaining(DateTime? startDate, DateTime? dueDate) {
    if (startDate == null) return 'Unknown';

    dueDate ??= startDate.add(const Duration(days: 280));

    final now = DateTime.now();
    final daysRemaining = dueDate.difference(now).inDays;

    if (daysRemaining < 0) {
      return 'Past due by ${-daysRemaining} days';
    } else if (daysRemaining == 0) {
      return 'Due today';
    } else {
      return '$daysRemaining days remaining';
    }
  }
}
