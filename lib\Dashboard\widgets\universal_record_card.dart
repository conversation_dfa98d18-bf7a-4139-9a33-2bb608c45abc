import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';

/// Universal Record Card System
/// Based on the Weight Card as the ideal template
/// Supports all record types across modules with consistent layout and behavior

class UniversalRecordCard extends StatelessWidget {
  // Simple variables for easy configuration
  final String row1Left;   // Date or left content for row 1
  final String row1Right;  // Primary value or right content for row 1
  final String row2Left;   // Left content for row 2
  final String row2Right;  // Right content for row 2
  final String? row3Left;  // Optional left content for row 3
  final String? row3Right; // Optional right content for row 3
  final String? notes;     // Optional notes row
  final Color primaryColor;
  final Color? row1RightColor; // For dynamic red/green based on income/expense
  final IconData? row1LeftIcon;  // Icon for row1Left content (usually calendar for date)
  final IconData? row2LeftIcon;  // Icon for row2Left content
  final IconData? row2RightIcon; // Icon for row2Right content
  final IconData? row3LeftIcon;  // Icon for row3Left content
  final IconData? row3RightIcon; // Icon for row3Right content
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool isSelectionMode;
  final ValueChanged<bool?>? onSelectionChanged;
  final bool compact;

  const UniversalRecordCard({
    Key? key,
    required this.row1Left,
    required this.row1Right,
    required this.row2Left,
    required this.row2Right,
    this.row3Left,
    this.row3Right,
    this.notes,
    required this.primaryColor,
    this.row1RightColor,
    this.row1LeftIcon,
    this.row2LeftIcon,
    this.row2RightIcon,
    this.row3LeftIcon,
    this.row3RightIcon,
    this.onEdit,
    this.onDelete,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onSelectionChanged,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: compact
          ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(
          color: Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: isSelectionMode
            ? () => onSelectionChanged?.call(!isSelected)
            : onTap,
        onLongPress: onLongPress,
        child: Container(
          padding: compact
              ? const EdgeInsets.all(12)
              : const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Date + Primary Value + Kebab Menu
              _buildRow1(),
              const SizedBox(height: 8),
              // Row 2: Left + Right content
              _buildRow2(),
              // Row 3: Optional third row
              if (row3Left != null || row3Right != null) ...[
                const SizedBox(height: 8),
                _buildRow3(),
              ],
              // Notes row (if available)
              if (notes?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                _buildNotesRow(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRow1() {
    return IntrinsicHeight(
      child: Row(
        children: [
          if (isSelectionMode)
            SizedBox(
              width: 56,
              child: Checkbox(
                value: isSelected,
                onChanged: onSelectionChanged,
                activeColor: AppColors.primary,
              ),
            ),
          // Status indicator - 4px vertical stripe
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          // Left content - flexible width with icon
          Expanded(
            child: Row(
              children: [
                if (row1LeftIcon != null) ...[
                  Icon(
                    row1LeftIcon,
                    size: 20,
                    color: const Color(0xFF303F9F), // Indigo
                  ),
                  const SizedBox(width: 8),
                ],
                Flexible(
                  child: Text(
                    row1Left,
                    style: const TextStyle(
                      color: Color(0xFF303F9F), // Indigo
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Right content - right aligned
          Container(
            constraints: const BoxConstraints(minWidth: 80),
            child: Text(
              row1Right,
              style: TextStyle(
                color: row1RightColor ?? primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
            ),
          ),
          // Kebab menu for edit/delete
          if (!isSelectionMode && (onEdit != null || onDelete != null))
            SizedBox(
              width: 24,
              child: _buildKebabMenu(),
            ),
        ],
      ),
    );
  }

  Widget _buildRow2() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        // Left content - flexible width with icon
        Expanded(
          child: Row(
            children: [
              if (row2LeftIcon != null) ...[
                Icon(
                  row2LeftIcon,
                  size: 20,
                  color: const Color(0xFF7B1FA2), // Purple
                ),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: Text(
                  row2Left,
                  style: const TextStyle(
                    color: Color(0xFF7B1FA2), // Purple
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // Right content - right aligned with icon
        Container(
          constraints: const BoxConstraints(minWidth: 100),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (row2RightIcon != null) ...[
                Icon(
                  row2RightIcon,
                  size: 20,
                  color: const Color(0xFF2E7D32), // Green
                ),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: Text(
                  row2Right,
                  style: const TextStyle(
                    color: Color(0xFF2E7D32), // Green
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.right,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ),
        if (!isSelectionMode && (onEdit != null || onDelete != null))
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildRow3() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        // Left content - flexible width with icon
        Expanded(
          child: Row(
            children: [
              if (row3LeftIcon != null) ...[
                Icon(
                  row3LeftIcon,
                  size: 18,
                  color: const Color(0xFF795548), // Brown
                ),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: Text(
                  row3Left ?? '',
                  style: const TextStyle(
                    color: Color(0xFF795548), // Brown
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // Right content - right aligned with icon
        if (row3Right?.isNotEmpty == true)
          Container(
            constraints: const BoxConstraints(minWidth: 100),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (row3RightIcon != null) ...[
                  Icon(
                    row3RightIcon,
                    size: 18,
                    color: const Color(0xFF795548), // Brown
                  ),
                  const SizedBox(width: 8),
                ],
                Flexible(
                  child: Text(
                    row3Right!,
                    style: const TextStyle(
                      color: Color(0xFF795548), // Brown
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        if (!isSelectionMode && (onEdit != null || onDelete != null))
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildNotesRow() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            notes!,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF16A085), // Teal
              fontStyle: FontStyle.italic,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (!isSelectionMode && (onEdit != null || onDelete != null))
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildKebabMenu() {
    return PopupMenuButton<String>(
      icon: const Icon(
        Icons.more_vert,
        color: Color(0xFF303F9F), // Indigo
      ),
      onSelected: (String choice) {
        if (choice == 'Edit' && onEdit != null) {
          onEdit!();
        } else if (choice == 'Delete' && onDelete != null) {
          onDelete!();
        }
      },
      itemBuilder: (BuildContext context) => [
        if (onEdit != null)
          const PopupMenuItem(
            value: 'Edit',
            child: Row(
              children: [
                Icon(Icons.edit_outlined),
                SizedBox(width: 8),
                Text('Edit'),
              ],
            ),
          ),
        if (onDelete != null)
          const PopupMenuItem(
            value: 'Delete',
            child: Row(
              children: [
                Icon(
                  Icons.delete_outline,
                  color: Colors.red,
                ),
                SizedBox(width: 8),
                Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
          ),
      ],
    );
  }

}

/// Simple theme constants for consistent colors across all record types
class UniversalRecordTheme {
  // Module-specific primary colors for the 4px stripe
  static const Color weightPrimary = Color(0xFF1976D2);       // Blue
  static const Color healthPrimary = Color(0xFF00796B);       // Teal
  static const Color milkPrimary = Color(0xFF2E7D32);         // Green
  static const Color breedingPrimary = Color(0xFFD32F2F);     // Red
  static const Color eventPrimary = Color(0xFF7B1FA2);        // Purple
  static const Color pregnancyPrimary = Color(0xFF9C27B0);    // Purple (distinct from events)
  static const Color deliveryPrimary = Color(0xFF2E7D32);     // Green (same as milk for consistency)
  static const Color transactionPrimary = Color(0xFFFF8F00);  // Orange
}
