import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';

/// Universal Record Card System
/// Based on the Weight Card as the ideal template
/// Supports all record types across modules with consistent layout and behavior

class UniversalRecordCard extends StatelessWidget {
  final UniversalRecordConfig config;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ValueChanged<bool?>? onSelectionChanged;
  final bool compact;

  const UniversalRecordCard({
    Key? key,
    required this.config,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: compact
          ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(
          color: Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: isSelectionMode
            ? () => onSelectionChanged?.call(!isSelected)
            : onTap,
        onLongPress: onLongPress,
        child: Container(
          padding: compact
              ? const EdgeInsets.all(12)
              : const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Date + Primary Value + Menu/Checkbox
              _buildRow1(),
              const SizedBox(height: 8),
              // Row 2: Cattle + Secondary Info
              _buildRow2(),
              // Row 3: Optional additional fields
              if (config.row3Fields.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildRow3(),
              ],
              // Notes row (if available)
              if (config.notesField != null) ...[
                const SizedBox(height: 8),
                _buildNotesRow(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRow1() {
    return IntrinsicHeight(
      child: Row(
        children: [
          if (isSelectionMode)
            SizedBox(
              width: 56,
              child: Checkbox(
                value: isSelected,
                onChanged: onSelectionChanged,
                activeColor: AppColors.primary,
              ),
            ),
          // Status indicator - 4px vertical stripe
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: config.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          // Date field - flexible width
          Expanded(
            child: _buildField(config.dateField),
          ),
          const SizedBox(width: 16),
          // Primary value - right aligned with minimum width
          Container(
            constraints: const BoxConstraints(minWidth: 80),
            child: _buildField(config.primaryValueField, rightAlign: true),
          ),
          if (!isSelectionMode && config.actions.isNotEmpty)
            SizedBox(
              width: 24,
              child: _buildActionsMenu(),
            ),
        ],
      ),
    );
  }

  Widget _buildRow2() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        // Cattle field - flexible width
        Expanded(
          child: _buildField(config.cattleField),
        ),
        const SizedBox(width: 16),
        // Secondary field - right aligned with minimum width
        if (config.secondaryField != null)
          Container(
            constraints: const BoxConstraints(minWidth: 100),
            child: _buildField(config.secondaryField!, rightAlign: true),
          ),
        if (!isSelectionMode && config.actions.isNotEmpty)
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildRow3() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        Expanded(
          child: Wrap(
            spacing: 16,
            runSpacing: 8,
            children: config.row3Fields.map((field) => _buildField(field)).toList(),
          ),
        ),
        if (!isSelectionMode && config.actions.isNotEmpty)
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildNotesRow() {
    return Row(
      children: [
        if (isSelectionMode)
          const SizedBox(width: 56),
        const SizedBox(width: 16),
        Expanded(
          child: _buildField(config.notesField!),
        ),
        if (!isSelectionMode && config.actions.isNotEmpty)
          const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildField(UniversalRecordField field, {bool rightAlign = false}) {
    return Row(
      mainAxisAlignment: rightAlign ? MainAxisAlignment.end : MainAxisAlignment.start,
      mainAxisSize: rightAlign ? MainAxisSize.min : MainAxisSize.max,
      children: [
        if (field.icon != null) ...[
          Icon(
            field.icon,
            size: 20,
            color: field.color,
          ),
          const SizedBox(width: 8),
        ],
        Flexible(
          child: Text(
            field.value,
            style: TextStyle(
              color: field.color,
              fontSize: field.fontSize,
              fontWeight: field.fontWeight,
              fontStyle: field.fontStyle,
            ),
            textAlign: rightAlign ? TextAlign.right : TextAlign.left,
            maxLines: field.maxLines,
            overflow: field.maxLines == 1 ? TextOverflow.ellipsis : TextOverflow.fade,
          ),
        ),
      ],
    );
  }

  Widget _buildActionsMenu() {
    return PopupMenuButton<UniversalRecordAction>(
      icon: Icon(
        Icons.more_vert,
        color: const Color(0xFF303F9F),
      ),
      onSelected: (action) => action.onPressed(),
      itemBuilder: (BuildContext context) => config.actions.map((action) {
        return PopupMenuItem(
          value: action,
          child: Row(
            children: [
              Icon(
                action.icon,
                color: action.color,
              ),
              const SizedBox(width: 8),
              Text(
                action.label,
                style: TextStyle(color: action.color),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

/// Configuration class for Universal Record Card
class UniversalRecordConfig {
  final Color primaryColor;
  final UniversalRecordField dateField;
  final UniversalRecordField primaryValueField;
  final UniversalRecordField cattleField;
  final UniversalRecordField? secondaryField;
  final List<UniversalRecordField> row3Fields;
  final UniversalRecordField? notesField;
  final List<UniversalRecordAction> actions;

  const UniversalRecordConfig({
    required this.primaryColor,
    required this.dateField,
    required this.primaryValueField,
    required this.cattleField,
    this.secondaryField,
    this.row3Fields = const [],
    this.notesField,
    this.actions = const [],
  });
}

/// Individual field configuration
class UniversalRecordField {
  final String value;
  final Color color;
  final IconData? icon;
  final double fontSize;
  final FontWeight fontWeight;
  final FontStyle fontStyle;
  final int maxLines;

  const UniversalRecordField({
    required this.value,
    required this.color,
    this.icon,
    this.fontSize = 16,
    this.fontWeight = FontWeight.normal,
    this.fontStyle = FontStyle.normal,
    this.maxLines = 1,
  });

  // Factory constructors for common field types
  factory UniversalRecordField.date(DateTime? date) {
    final formattedDate = date != null
        ? DateFormat('MMM dd, yyyy').format(date)
        : 'No date';
    return UniversalRecordField(
      value: formattedDate,
      color: UniversalRecordTheme.dateColor,
      icon: Icons.calendar_today_outlined,
      fontWeight: FontWeight.bold,
    );
  }

  factory UniversalRecordField.primaryValue(String value, {Color? color}) {
    return UniversalRecordField(
      value: value,
      color: color ?? UniversalRecordTheme.primaryValueColor,
      fontWeight: FontWeight.bold,
      fontSize: 16,
    );
  }

  factory UniversalRecordField.cattle(String cattleName, String? tagId) {
    final displayName = (tagId?.isNotEmpty == true) 
        ? '$cattleName ($tagId)' 
        : cattleName;
    return UniversalRecordField(
      value: displayName,
      color: UniversalRecordTheme.cattleColor,
      icon: Icons.pets,
      fontWeight: FontWeight.bold,
    );
  }

  factory UniversalRecordField.method(String method, IconData? icon) {
    return UniversalRecordField(
      value: method,
      color: UniversalRecordTheme.methodColor,
      icon: icon ?? Icons.build,
      fontWeight: FontWeight.bold,
    );
  }

  factory UniversalRecordField.status(String status, Color color) {
    return UniversalRecordField(
      value: status,
      color: color,
      fontWeight: FontWeight.bold,
    );
  }

  factory UniversalRecordField.cost(double? cost) {
    final formattedCost = cost != null ? '\$${cost.toStringAsFixed(2)}' : 'N/A';
    return UniversalRecordField(
      value: formattedCost,
      color: UniversalRecordTheme.costColor,
      icon: Icons.attach_money,
      fontWeight: FontWeight.w500,
    );
  }

  factory UniversalRecordField.metric(String label, String value, IconData icon) {
    return UniversalRecordField(
      value: '$label: $value',
      color: UniversalRecordTheme.metricColor,
      icon: icon,
      fontWeight: FontWeight.w500,
      fontSize: 14,
    );
  }

  factory UniversalRecordField.notes(String notes) {
    return UniversalRecordField(
      value: notes,
      color: UniversalRecordTheme.notesColor,
      fontStyle: FontStyle.italic,
      fontSize: 14,
      maxLines: 2,
    );
  }
}

/// Action configuration
class UniversalRecordAction {
  final String label;
  final IconData icon;
  final Color? color;
  final VoidCallback onPressed;

  const UniversalRecordAction({
    required this.label,
    required this.icon,
    this.color,
    required this.onPressed,
  });

  factory UniversalRecordAction.edit(VoidCallback onEdit) {
    return UniversalRecordAction(
      label: 'Edit',
      icon: Icons.edit_outlined,
      onPressed: onEdit,
    );
  }

  factory UniversalRecordAction.delete(VoidCallback onDelete) {
    return UniversalRecordAction(
      label: 'Delete',
      icon: Icons.delete_outline,
      color: Colors.red,
      onPressed: onDelete,
    );
  }
}

/// Theme constants for consistent colors across all record types
class UniversalRecordTheme {
  // Standard colors for different data types (from weight card)
  static const Color dateColor = Color(0xFF303F9F);           // Indigo
  static const Color cattleColor = Color(0xFF7B1FA2);         // Purple
  static const Color methodColor = Color(0xFF2E7D32);         // Green
  static const Color notesColor = Color(0xFF16A085);          // Teal
  static const Color costColor = Color(0xFFD32F2F);           // Red
  static const Color metricColor = Color(0xFF795548);         // Brown
  static const Color primaryValueColor = Color(0xFF1976D2);   // Blue

  // Module-specific primary colors for the 4px stripe
  static const Color weightPrimary = Color(0xFF1976D2);       // Blue
  static const Color healthPrimary = Color(0xFF00796B);       // Teal
  static const Color milkPrimary = Color(0xFF2E7D32);         // Green
  static const Color breedingPrimary = Color(0xFFD32F2F);     // Red
  static const Color eventPrimary = Color(0xFF7B1FA2);        // Purple
  static const Color pregnancyPrimary = Color(0xFF9C27B0);    // Purple (distinct from events)
  static const Color deliveryPrimary = Color(0xFF2E7D32);     // Green (same as milk for consistency)
  static const Color transactionPrimary = Color(0xFFFF8F00);  // Orange
}

/// Helper class for creating common record configurations
class UniversalRecordConfigFactory {
  /// Weight Record Configuration
  static UniversalRecordConfig weight({
    required DateTime? measurementDate,
    required double weight,
    required String cattleName,
    required String? tagId,
    required String? measurementMethod,
    double? bodyConditionScore,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final methodIcon = _getWeightMethodIcon(measurementMethod);

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.weightPrimary,
      dateField: UniversalRecordField.date(measurementDate),
      primaryValueField: UniversalRecordField.primaryValue(
        '${weight.toStringAsFixed(1)} kg',
        color: UniversalRecordTheme.weightPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: measurementMethod != null
          ? UniversalRecordField.method(_getWeightMethodDisplayName(measurementMethod), methodIcon)
          : null,
      row3Fields: [
        if (bodyConditionScore != null)
          UniversalRecordField.metric(
            'BCS',
            bodyConditionScore.toStringAsFixed(1),
            Icons.fitness_center,
          ),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Health Record Configuration
  static UniversalRecordConfig health({
    required DateTime? date,
    required String condition,
    required String treatment,
    required String cattleName,
    required String? tagId,
    String? medicine,
    double? dose,
    String? dosageUnit,
    String? veterinarian,
    double? cost,
    bool? isResolved,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.healthPrimary,
      dateField: UniversalRecordField.date(date),
      primaryValueField: UniversalRecordField.primaryValue(
        condition,
        color: UniversalRecordTheme.healthPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField(
        value: treatment,
        color: UniversalRecordTheme.methodColor,
        icon: Icons.medical_services,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        if (medicine?.isNotEmpty == true)
          UniversalRecordField.metric(
            'Medicine',
            '$medicine${dose != null ? ' (${dose.toString()} ${dosageUnit ?? ''})' : ''}',
            Icons.medication,
          ),
        if (veterinarian?.isNotEmpty == true)
          UniversalRecordField.metric('Vet', veterinarian!, Icons.person),
        if (cost != null)
          UniversalRecordField.cost(cost),
        if (isResolved == true)
          UniversalRecordField.status('Resolved', Colors.green),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Milk Record Configuration
  static UniversalRecordConfig milk({
    required DateTime? date,
    required double totalAmount,
    required String cattleName,
    required String? tagId,
    double? morningAmount,
    double? eveningAmount,
    double? fatContent,
    double? pricePerLiter,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.milkPrimary,
      dateField: UniversalRecordField.date(date),
      primaryValueField: UniversalRecordField.primaryValue(
        '${totalAmount.toStringAsFixed(1)} L',
        color: UniversalRecordTheme.milkPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField(
        value: 'Total Yield',
        color: UniversalRecordTheme.methodColor,
        icon: Icons.water_drop,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        if (morningAmount != null)
          UniversalRecordField.metric(
            'Morning',
            '${morningAmount.toStringAsFixed(1)} L',
            Icons.wb_sunny_outlined,
          ),
        if (eveningAmount != null)
          UniversalRecordField.metric(
            'Evening',
            '${eveningAmount.toStringAsFixed(1)} L',
            Icons.nightlight_outlined,
          ),
        if (fatContent != null)
          UniversalRecordField.metric(
            'Fat',
            '${fatContent.toStringAsFixed(1)}%',
            Icons.opacity,
          ),
        if (pricePerLiter != null)
          UniversalRecordField.cost(pricePerLiter),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Breeding Record Configuration
  static UniversalRecordConfig breeding({
    required DateTime? breedingDate,
    required String status,
    required String cattleName,
    required String? tagId,
    String? method,
    String? bullOrSemen,
    DateTime? expectedDate,
    double? cost,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
    VoidCallback? onStatusTap,
  }) {
    final statusColor = _getBreedingStatusColor(status);

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.breedingPrimary,
      dateField: UniversalRecordField.date(breedingDate),
      primaryValueField: UniversalRecordField.status(status, statusColor),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: method != null
          ? UniversalRecordField.method(method, Icons.favorite)
          : null,
      row3Fields: [
        if (bullOrSemen?.isNotEmpty == true)
          UniversalRecordField.metric('Bull/Semen', bullOrSemen!, Icons.pets),
        if (expectedDate != null)
          UniversalRecordField.metric(
            'Expected',
            DateFormat('MMM dd').format(expectedDate),
            Icons.event,
          ),
        if (cost != null)
          UniversalRecordField.cost(cost),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
        if (onStatusTap != null)
          UniversalRecordAction(
            label: 'Change Status',
            icon: Icons.update,
            onPressed: onStatusTap,
          ),
      ],
    );
  }

  /// Event Record Configuration
  static UniversalRecordConfig event({
    required String title,
    required DateTime? eventDate,
    required String cattleName,
    required String? tagId,
    String? eventType,
    String? priority,
    bool isCompleted = false,
    bool isMissed = false,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final statusColor = isCompleted
        ? Colors.green
        : isMissed
            ? Colors.red
            : UniversalRecordTheme.eventPrimary;

    final statusText = isCompleted
        ? 'Completed'
        : isMissed
            ? 'Missed'
            : 'Pending';

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.eventPrimary,
      dateField: UniversalRecordField.date(eventDate),
      primaryValueField: UniversalRecordField.primaryValue(
        title,
        color: UniversalRecordTheme.eventPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField.status(statusText, statusColor),
      row3Fields: [
        if (eventType?.isNotEmpty == true)
          UniversalRecordField.metric('Type', eventType!, Icons.category),
        if (priority?.isNotEmpty == true)
          UniversalRecordField.metric('Priority', priority!, Icons.priority_high),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Pregnancy Record Configuration
  static UniversalRecordConfig pregnancy({
    required DateTime? startDate,
    required String status,
    required String cattleName,
    required String? tagId,
    DateTime? expectedCalvingDate,
    DateTime? actualCalvingDate,
    String? breedingRecordId,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
    VoidCallback? onStatusTap,
  }) {
    final statusColor = _getPregnancyStatusColor(status);
    final pregnancyStage = _calculatePregnancyStage(startDate);
    final daysRemaining = _calculateDaysRemaining(startDate, expectedCalvingDate);

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.pregnancyPrimary,
      dateField: UniversalRecordField.date(startDate),
      primaryValueField: UniversalRecordField.status(status, statusColor),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField(
        value: pregnancyStage,
        color: UniversalRecordTheme.pregnancyPrimary,
        icon: Icons.pregnant_woman,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        if (expectedCalvingDate != null)
          UniversalRecordField.metric(
            status.toLowerCase() == 'completed' ? 'Delivery Date' : 'Expected Date',
            DateFormat('MMM dd, yyyy').format(
              status.toLowerCase() == 'completed' && actualCalvingDate != null
                  ? actualCalvingDate
                  : expectedCalvingDate
            ),
            Icons.today,
          ),
        UniversalRecordField.metric(
          status.toLowerCase() == 'completed' ? 'Duration' : 'Remaining',
          daysRemaining,
          Icons.hourglass_bottom,
        ),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
        if (onStatusTap != null)
          UniversalRecordAction(
            label: 'Change Status',
            icon: Icons.update,
            onPressed: onStatusTap,
          ),
      ],
    );
  }

  /// Delivery Record Configuration
  static UniversalRecordConfig delivery({
    required DateTime? deliveryDate,
    required String deliveryType,
    required String cattleName,
    required String? tagId,
    int? calfCount,
    List<Map<String, dynamic>>? calfDetails,
    bool? hadComplications,
    String? complicationDetails,
    String? veterinarian,
    String? notes,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final calfCountText = calfCount != null ? calfCount.toString() : '1';
    final complicationStatus = hadComplications == true ? 'Had Complications' : 'Normal';
    final complicationColor = hadComplications == true ? Colors.red : Colors.green;

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.deliveryPrimary,
      dateField: UniversalRecordField.date(deliveryDate),
      primaryValueField: UniversalRecordField.primaryValue(
        deliveryType,
        color: UniversalRecordTheme.deliveryPrimary,
      ),
      cattleField: UniversalRecordField.cattle(cattleName, tagId),
      secondaryField: UniversalRecordField.metric(
        'Calves',
        calfCountText,
        Icons.child_care,
      ),
      row3Fields: [
        UniversalRecordField.status(complicationStatus, complicationColor),
        if (veterinarian?.isNotEmpty == true)
          UniversalRecordField.metric('Vet', veterinarian!, Icons.person),
        if (calfDetails?.isNotEmpty == true)
          UniversalRecordField.metric(
            'Calf Details',
            '${calfDetails!.length} calf${calfDetails.length > 1 ? 'ves' : ''} recorded',
            Icons.baby_changing_station,
          ),
      ],
      notesField: notes?.isNotEmpty == true ? UniversalRecordField.notes(notes!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  /// Transaction Record Configuration
  static UniversalRecordConfig transaction({
    required DateTime? date,
    required String title,
    required double amount,
    required String categoryType, // 'Income' or 'Expense'
    required String category,
    required String paymentMethod,
    String? description,
    IconData? categoryIcon,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    final isIncome = categoryType.toLowerCase() == 'income';
    final amountColor = isIncome ? Colors.green : Colors.red;
    final formattedAmount = '${isIncome ? '+' : '-'}\$${amount.toStringAsFixed(2)}';
    final statusIcon = isIncome ? Icons.arrow_upward : Icons.arrow_downward;

    return UniversalRecordConfig(
      primaryColor: UniversalRecordTheme.transactionPrimary,
      dateField: UniversalRecordField.date(date),
      primaryValueField: UniversalRecordField.primaryValue(
        formattedAmount,
        color: amountColor,
      ),
      cattleField: UniversalRecordField(
        value: title,
        color: UniversalRecordTheme.transactionPrimary,
        icon: categoryIcon ?? (isIncome ? Icons.trending_up : Icons.trending_down),
        fontWeight: FontWeight.bold,
      ),
      secondaryField: UniversalRecordField(
        value: categoryType,
        color: amountColor,
        icon: statusIcon,
        fontWeight: FontWeight.bold,
      ),
      row3Fields: [
        UniversalRecordField.metric('Category', category, Icons.category),
        UniversalRecordField.metric('Payment', paymentMethod, Icons.payment),
      ],
      notesField: description?.isNotEmpty == true ? UniversalRecordField.notes(description!) : null,
      actions: [
        UniversalRecordAction.edit(onEdit),
        UniversalRecordAction.delete(onDelete),
      ],
    );
  }

  // Helper methods for weight module
  static IconData _getWeightMethodIcon(String? method) {
    switch (method?.toLowerCase()) {
      case 'scale':
        return Icons.scale;
      case 'tape':
        return Icons.straighten;
      case 'visual_estimate':
        return Icons.visibility;
      default:
        return Icons.straighten;
    }
  }

  static String _getWeightMethodDisplayName(String? method) {
    switch (method?.toLowerCase()) {
      case 'scale':
        return 'Scale';
      case 'tape':
        return 'Tape';
      case 'visual_estimate':
        return 'Visual';
      default:
        return method ?? 'Unknown';
    }
  }

  // Helper method for breeding status colors
  static Color _getBreedingStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'successful':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return UniversalRecordTheme.breedingPrimary;
    }
  }

  // Helper method for pregnancy status colors
  static Color _getPregnancyStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return const Color(0xFF1976D2); // Blue
      case 'confirmed':
        return const Color(0xFF2E7D32); // Green
      case 'abortion':
        return const Color(0xFFD32F2F); // Red
      case 'completed':
        return const Color(0xFF9C27B0); // Purple
      default:
        return Colors.grey;
    }
  }

  // Helper method to calculate pregnancy stage
  static String _calculatePregnancyStage(DateTime? startDate) {
    if (startDate == null) return 'Unknown Stage';

    final now = DateTime.now();
    final daysSinceStart = now.difference(startDate).inDays;

    if (daysSinceStart < 90) {
      return 'Early Stage';
    } else if (daysSinceStart < 180) {
      return 'Mid Stage';
    } else {
      return 'Late Stage';
    }
  }

  // Helper method to calculate days remaining until due date
  static String _calculateDaysRemaining(DateTime? startDate, DateTime? dueDate) {
    if (startDate == null) return 'Unknown';

    dueDate ??= startDate.add(const Duration(days: 280));

    final now = DateTime.now();
    final daysRemaining = dueDate.difference(now).inDays;

    if (daysRemaining < 0) {
      return 'Past due by ${-daysRemaining} days';
    } else if (daysRemaining == 0) {
      return 'Due today';
    } else {
      return '$daysRemaining days remaining';
    }
  }
}
